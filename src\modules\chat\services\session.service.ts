import { IChatMessage, IChatSession } from "../types/chat.types";

export class SessionService {
	private readonly STORAGE_KEY = "chat-sessions";
	private readonly MAX_SESSIONS = 10;
	private readonly SESSION_EXPIRY_DAYS = 7;

	/**
	 * Get all stored sessions
	 */
	getAllSessions(): IChatSession[] {
		if (typeof window === "undefined") return [];

		try {
			const stored = localStorage.getItem(this.STORAGE_KEY);
			if (!stored) return [];

			const sessions: IChatSession[] = JSON.parse(stored);
			return this.cleanExpiredSessions(sessions);
		} catch (error) {
			console.error("Error loading sessions:", error);
			return [];
		}
	}

	/**
	 * Get a specific session by ID
	 */
	getSession(sessionId: string): IChatSession | null {
		const sessions = this.getAllSessions();
		return sessions.find(session => session.id === sessionId) || null;
	}

	/**
	 * Save a session
	 */
	saveSession(session: IChatSession): void {
		if (typeof window === "undefined") return;

		try {
			const sessions = this.getAllSessions();
			const existingIndex = sessions.findIndex(s => s.id === session.id);

			if (existingIndex >= 0) {
				// Update existing session
				sessions[existingIndex] = {
					...session,
					updatedAt: new Date(),
				};
			} else {
				// Add new session
				sessions.unshift({
					...session,
					updatedAt: new Date(),
				});
			}

			// Keep only the most recent sessions
			const limitedSessions = sessions.slice(0, this.MAX_SESSIONS);

			localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedSessions));
		} catch (error) {
			console.error("Error saving session:", error);
		}
	}

	/**
	 * Delete a session
	 */
	deleteSession(sessionId: string): void {
		if (typeof window === "undefined") return;

		try {
			const sessions = this.getAllSessions();
			const filteredSessions = sessions.filter(session => session.id !== sessionId);
			localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredSessions));
		} catch (error) {
			console.error("Error deleting session:", error);
		}
	}

	/**
	 * Clear all sessions
	 */
	clearAllSessions(): void {
		if (typeof window === "undefined") return;

		try {
			localStorage.removeItem(this.STORAGE_KEY);
		} catch (error) {
			console.error("Error clearing sessions:", error);
		}
	}

	/**
	 * Add a message to a session
	 */
	addMessageToSession(sessionId: string, message: IChatMessage): IChatSession | null {
		const session = this.getSession(sessionId);
		if (!session) return null;

		const updatedSession: IChatSession = {
			...session,
			messages: [...session.messages, message],
			updatedAt: new Date(),
		};

		this.saveSession(updatedSession);
		return updatedSession;
	}

	/**
	 * Update a message in a session
	 */
	updateMessageInSession(sessionId: string, messageId: string, updates: Partial<IChatMessage>): IChatSession | null {
		const session = this.getSession(sessionId);
		if (!session) return null;

		const updatedMessages = session.messages.map(msg => (msg.id === messageId ? { ...msg, ...updates } : msg));

		const updatedSession: IChatSession = {
			...session,
			messages: updatedMessages,
			updatedAt: new Date(),
		};

		this.saveSession(updatedSession);
		return updatedSession;
	}

	/**
	 * Get session context for API requests
	 */
	getSessionContext(sessionId: string): Record<string, unknown> {
		const session = this.getSession(sessionId);
		if (!session) return {};

		// Return relevant context information
		return {
			sessionId,
			messageCount: session.messages.length,
			lastMessageTime: session.messages[session.messages.length - 1]?.timestamp,
			sessionStartTime: session.createdAt,
		};
	}

	/**
	 * Clean expired sessions
	 */
	private cleanExpiredSessions(sessions: IChatSession[]): IChatSession[] {
		const expiryDate = new Date();
		expiryDate.setDate(expiryDate.getDate() - this.SESSION_EXPIRY_DAYS);

		return sessions.filter(session => {
			const sessionDate = new Date(session.updatedAt);
			return sessionDate > expiryDate;
		});
	}

	/**
	 * Generate a unique session ID
	 */
	generateSessionId(): string {
		return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
	}

	/**
	 * Create a new session
	 */
	createSession(): IChatSession {
		const sessionId = this.generateSessionId();
		const newSession: IChatSession = {
			id: sessionId,
			messages: [],
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		this.saveSession(newSession);
		return newSession;
	}

	/**
	 * Get the most recent session
	 */
	getLatestSession(): IChatSession | null {
		const sessions = this.getAllSessions();
		return sessions.length > 0 ? sessions[0] : null;
	}
}

// Export singleton instance
export const sessionService = new SessionService();
