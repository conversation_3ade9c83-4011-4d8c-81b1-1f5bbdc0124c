import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { IChatMessage, IChatSession, IChatState } from "../types/chat.types";

// Generate unique IDs
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// Base atoms
export const chatIsOpenAtom = atom<boolean>(false);
export const chatIsStreamingAtom = atom<boolean>(false);
export const chatIsLoadingAtom = atom<boolean>(false);
export const chatErrorAtom = atom<string | null>(null);
export const streamingMessageIdAtom = atom<string | null>(null);

export const currentSessionAtom = atomWithStorage<IChatSession | null>("chat-session", null);

// Derived atoms
export const chatStateAtom = atom<IChatState>(get => ({
	currentSession: get(currentSessionAtom),
	isOpen: get(chatIsOpenAtom),
	isStreaming: get(chatIsStreamingAtom),
	isLoading: get(chatIsLoadingAtom),
	error: get(chatErrorAtom),
	streamingMessageId: get(streamingMessageIdAtom),
}));

// Messages atom (derived from current session)
export const messagesAtom = atom<IChatMessage[]>(get => {
	const session = get(currentSessionAtom);
	return session?.messages || [];
});

// Action atoms
export const openChatAtom = atom(null, (get, set) => {
	set(chatIsOpenAtom, true);
	set(chatErrorAtom, null);
});

export const closeChatAtom = atom(null, (get, set) => {
	set(chatIsOpenAtom, false);
	set(chatErrorAtom, null);
});

export const toggleChatAtom = atom(null, (get, set) => {
	const isOpen = get(chatIsOpenAtom);
	set(chatIsOpenAtom, !isOpen);
	if (!isOpen) {
		set(chatErrorAtom, null);
	}
});

export const createSessionAtom = atom(null, (get, set) => {
	const sessionId = generateId();
	const newSession: IChatSession = {
		id: sessionId,
		messages: [],
		createdAt: new Date(),
		updatedAt: new Date(),
	};
	set(currentSessionAtom, newSession);
	return sessionId;
});

export const addMessageAtom = atom(null, (get, set, message: Omit<IChatMessage, "id" | "timestamp">) => {
	const session = get(currentSessionAtom);
	if (!session) {
		// Create new session if none exists
		set(createSessionAtom);
		const newSession = get(currentSessionAtom);
		if (!newSession) return;
	}

	const currentSession = get(currentSessionAtom);
	if (!currentSession) return;

	const newMessage: IChatMessage = {
		...message,
		id: generateId(),
		timestamp: new Date(),
	};

	const updatedSession: IChatSession = {
		...currentSession,
		messages: [...currentSession.messages, newMessage],
		updatedAt: new Date(),
	};

	set(currentSessionAtom, updatedSession);
	return newMessage.id;
});

export const updateMessageAtom = atom(null, (get, set, messageId: string, updates: Partial<IChatMessage>) => {
	const session = get(currentSessionAtom);
	if (!session) return;

	const updatedMessages = session.messages.map(msg => (msg.id === messageId ? { ...msg, ...updates } : msg));

	const updatedSession: IChatSession = {
		...session,
		messages: updatedMessages,
		updatedAt: new Date(),
	};

	set(currentSessionAtom, updatedSession);
});

export const setStreamingStateAtom = atom(null, (get, set, isStreaming: boolean, messageId?: string) => {
	set(chatIsStreamingAtom, isStreaming);
	set(streamingMessageIdAtom, messageId || null);
});

export const setLoadingStateAtom = atom(null, (get, set, isLoading: boolean) => {
	set(chatIsLoadingAtom, isLoading);
});

export const setErrorAtom = atom(null, (get, set, error: string | null) => {
	set(chatErrorAtom, error);
});

export const clearSessionAtom = atom(null, (get, set) => {
	set(currentSessionAtom, null);
	set(chatErrorAtom, null);
	set(chatIsStreamingAtom, false);
	set(chatIsLoadingAtom, false);
	set(streamingMessageIdAtom, null);
});

// Computed atoms
export const hasMessagesAtom = atom(get => {
	const messages = get(messagesAtom);
	return messages.length > 0;
});

export const lastMessageAtom = atom(get => {
	const messages = get(messagesAtom);
	return messages[messages.length - 1] || null;
});

export const sessionIdAtom = atom(get => {
	const session = get(currentSessionAtom);
	return session?.id || null;
});
