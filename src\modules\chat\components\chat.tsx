import { useChat } from "../hooks/chat-ia.hook";
import { ChatSidebar } from "./sidebar";
import { ChatTrigger } from "./trigger";

interface ChatProps {
	showTrigger?: boolean;
	triggerPosition?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
	triggerSize?: "sm" | "md" | "lg";
	className?: string;
	useSidebar?: boolean;
}

export const Chat = ({ showTrigger = true, triggerPosition = "bottom-right", triggerSize = "md", className, useSidebar = true }: ChatProps) => {
	const { sendMessage, stopStreaming } = useChat();

	return (
		<>
			{showTrigger && <ChatTrigger position={triggerPosition} size={triggerSize} className={className} />}{" "}
			<ChatSidebar onSendMessage={sendMessage} onStopStreaming={stopStreaming} />
		</>
	);
};
