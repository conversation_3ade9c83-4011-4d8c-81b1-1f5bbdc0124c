"use client";
import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { inspectionKeys } from "../../../constants/query/keys";

export const useDeleteFormMutation = () => {
	const queryClient = useQueryClient();

	const deleteFormMutation = useMutation({
		mutationKey: inspectionKeys.forms.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(INSPECTION_FORM_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => inspectionKeys.forms.invalidateAll(queryClient),
	});

	return {
		deleteForm: (id: string) =>
			toast.promise(deleteFormMutation.mutateAsync(id), {
				loading: "Excluindo formulário...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};


