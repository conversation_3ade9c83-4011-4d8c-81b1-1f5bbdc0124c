"use client";
import { AnimatePresence, motion } from "framer-motion";
import { useAtom, useAtomValue } from "jotai";
import { MessageSquare, Trash2, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Button } from "../../../shared/components/shadcn/button";
import {
	chatIsOpenAtom,
	chatIsStreamingAtom,
	clearSessionAtom,
	closeChatAtom,
	hasMessagesAtom,
	messagesAtom,
	streamingMessageIdAtom,
} from "../atoms/chat-ia.atoms";
import { ChatInput } from "./input";
import { ChatMessage } from "./message";
import { TypingIndicator } from "./typing-indicador";

interface ChatSidebarProps {
	onSendMessage: (message: string) => void;
	onStopStreaming?: () => void;
	className?: string;
}

export const ChatSidebar = ({ onSendMessage, onStopStreaming, className }: ChatSidebarProps) => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	const [, closeChat] = useAtom(closeChatAtom);
	const [, clearSession] = useAtom(clearSessionAtom);

	const messages = useAtomValue(messagesAtom);
	const isStreaming = useAtomValue(chatIsStreamingAtom);
	const streamingMessageId = useAtomValue(streamingMessageIdAtom);
	const hasMessages = useAtomValue(hasMessagesAtom);

	const [inputValue, setInputValue] = useState("");
	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLTextAreaElement>(null);

	useEffect(() => {
		if (scrollAreaRef.current) {
			scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
		}
	}, [messages, isStreaming, streamingMessageId]);

	useEffect(() => {
		if (isOpen && inputRef.current) {
			setTimeout(() => inputRef.current?.focus(), 100);
		}
	}, [isOpen]);

	const handleSend = () => {
		if (inputValue.trim()) {
			onSendMessage(inputValue.trim());
			setInputValue("");
		}
	};

	const handleClearChat = () => {
		clearSession();
	};

	const handleClose = () => {
		closeChat();
	};

	return (
		<>
			<div
				className={`transition-height fixed right-[1rem] bottom-[6rem] flex w-[375px] max-w-[90%] touch-auto flex-col justify-between overflow-hidden rounded-3xl bg-white opacity-0 shadow-lg duration-300 ease-out sm:right-[4rem] sm:bottom-[7rem] ${
					isOpen ? "h-[500px] opacity-100" : "h-0 opacity-0"
				}`}
			>
				<div className="bg-background/95 flex items-center justify-between border-b p-4 backdrop-blur">
					<div className="flex items-center gap-2">
						<MessageSquare className="text-primary h-5 w-5" />
						<h2 className="text-lg font-semibold">S.I.M³P IA</h2>
					</div>

					<div className="flex items-center gap-2">
						{hasMessages && (
							<Button
								onClick={handleClearChat}
								variant="ghost"
								size="sm"
								className="text-muted-foreground hover:text-destructive"
								title="Limpar conversa"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						)}
						<Button onClick={handleClose} variant="ghost" size="sm" className="text-muted-foreground" title="Fechar chat">
							<X className="h-4 w-4" />
						</Button>
					</div>
				</div>

				<div ref={scrollAreaRef} className="flex-1 overflow-y-auto p-2">
					<AnimatePresence mode="wait">
						{!hasMessages ? (
							<motion.div
								key="welcome"
								initial={{ opacity: 0, scale: 0.9 }}
								animate={{ opacity: 1, scale: 1 }}
								exit={{ opacity: 0, scale: 0.9 }}
								transition={{ duration: 0.3 }}
								className="flex h-full flex-col items-center justify-center p-8 text-center"
							>
								<motion.div
									initial={{ scale: 0, rotate: -180 }}
									animate={{ scale: 1, rotate: 0 }}
									transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
								>
									<MessageSquare className="text-muted-foreground/50 mb-4 h-12 w-12" />
								</motion.div>
								<motion.h3
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: 0.4, duration: 0.3 }}
									className="text-muted-foreground mb-2 text-lg font-medium"
								>
									Bem-vindo ao Chat IA
								</motion.h3>
								<motion.p
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: 0.6, duration: 0.3 }}
									className="text-muted-foreground max-w-sm text-sm"
								>
									Faça perguntas, peça ajuda ou converse sobre qualquer assunto. Estou aqui para ajudar!
								</motion.p>
							</motion.div>
						) : (
							<motion.div
								key="messages"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								transition={{ duration: 0.2 }}
								className="space-y-1 p-2"
							>
								<AnimatePresence initial={false}>
									{messages.map((message, index) => (
										<motion.div
											key={message.id}
											initial={{ opacity: 0, y: 20, scale: 0.95 }}
											animate={{ opacity: 1, y: 0, scale: 1 }}
											exit={{ opacity: 0, y: -20, scale: 0.95 }}
											transition={{
												duration: 0.3,
												delay: index * 0.05, // Stagger effect
												type: "spring",
												stiffness: 500,
												damping: 30,
											}}
										>
											<ChatMessage message={message} isStreaming={isStreaming && message.id === streamingMessageId} />
										</motion.div>
									))}
								</AnimatePresence>
								{isStreaming && !streamingMessageId && (
									<motion.div
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: -20 }}
										transition={{ duration: 0.2 }}
									>
										<TypingIndicator />
									</motion.div>
								)}
							</motion.div>
						)}
					</AnimatePresence>
				</div>

				<div className="bg-background/95 border-t backdrop-blur">
					<ChatInput
						ref={inputRef}
						value={inputValue}
						onChange={setInputValue}
						onSend={handleSend}
						onStop={onStopStreaming}
						isStreaming={isStreaming}
						placeholder="Digite sua mensagem..."
						className="border-0"
					/>
				</div>
			</div>
		</>
	);
};
