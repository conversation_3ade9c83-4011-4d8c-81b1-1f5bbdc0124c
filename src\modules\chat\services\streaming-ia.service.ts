import { axiosInstance } from "../../../config/api/instance";
import { CHAT_ENDPOINTS } from "../api/endpoints";
import { IChatError, IChatStreamOptions, IChatStreamRequest } from "../types/chat.types";

export class StreamingIAService {
	private abortController: AbortController | null = null;

	async streamChat(request: IChatStreamRequest, options: IChatStreamOptions = {}): Promise<void> {
		console.log("🎯 Iniciando streamChat com:", request);

		this.abortStream();

		this.abortController = new AbortController();
		const signal = options.signal || this.abortController.signal;

		const url = `${axiosInstance.defaults.baseURL}${CHAT_ENDPOINTS.TO_TALK_STREAM}`;
		const headers = {
			"Content-Type": "application/json",
			...this.getAuthHeaders(),
		};

		console.log("🌐 URL:", url);
		console.log("📋 Headers:", headers);
		console.log(
			"📤 Body:",
			JSON.stringify({
				message: request.message,
				sessionId: request.sessionId,
				context: request.context || {},
				model: request.model || "llama2",
				temperature: request.temperature || 1,
			}),
		);

		try {
			const response = await fetch(url, {
				method: "POST",
				headers,
				body: JSON.stringify({
					message: request.message,
					sessionId: request.sessionId,
					context: request.context || {},
					model: request.model || "llama2",
					temperature: request.temperature || 1,
				}),
				signal,
				credentials: "include",
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error("❌ Erro na resposta:", response.status, errorText);
				throw new Error(`Erro HTTP! status: ${response.status} - ${errorText}`);
			}

			if (!response.body) {
				console.error("❌ Nenhuma resposta recebida");
				throw new Error(`Nenhuma resposta recebida`);
			}

			const reader = response.body.getReader();
			const decoder = new TextDecoder();
			let fullMessage = "";
			let buffer = "";

			console.log("🚀 Iniciando streaming...");

			try {
				while (true) {
					const { done, value } = await reader.read();
					if (done) {
						console.log("✅ Streaming finalizado");
						break;
					}

					const chunk = decoder.decode(value, { stream: true });
					buffer += chunk;

					const lines = buffer.split("\n");
					buffer = lines.pop() || "";

					for (const line of lines) {
						if (line.trim() === "") continue;

						console.log("📦 Linha recebida:", line);

						// Handle Server-Sent Events format
						if (line.startsWith("data: ")) {
							const data = line.slice(6).trim();

							if (data === "[DONE]") {
								console.log("🏁 Recebido [DONE], finalizando...");
								options.onComplete?.(fullMessage);
								return;
							}

							let content = "";
							try {
								// Try to parse as JSON first
								const parsed = JSON.parse(data);
								content = parsed.content || parsed.delta?.content || parsed.choices?.[0]?.delta?.content || parsed.text || "";
								console.log("📝 Conteúdo JSON:", content);
							} catch {
								// If not JSON, treat as plain text
								content = data;
								console.log("📝 Conteúdo texto:", content);
							}

							if (content) {
								fullMessage += content;
								console.log("🔄 Enviando chunk:", content);
								options.onChunk?.(content);
							}
						}
						// Handle plain text streaming (no SSE format)
						else if (line.trim()) {
							let content = "";
							try {
								// Try JSON first
								const parsed = JSON.parse(line);
								content = parsed.content || parsed.delta?.content || parsed.choices?.[0]?.delta?.content || parsed.text || "";
							} catch {
								// Plain text
								content = line.trim();
							}

							if (content) {
								fullMessage += content;
								console.log("🔄 Enviando chunk (plain):", content);
								options.onChunk?.(content);
							}
						}
					}
				}

				console.log("📋 Mensagem completa:", fullMessage);
				options.onComplete?.(fullMessage);
			} finally {
				reader.releaseLock();
			}
		} catch (error) {
			console.error("❌ Erro no streaming:", error);

			if (signal.aborted) {
				console.log("🛑 Stream foi cancelado pelo usuário");
				return;
			}

			const chatError: IChatError = {
				message: error instanceof Error ? error.message : "Ocorreu um erro desconhecido",
				details: error,
			};

			console.error("🚨 Erro processado:", chatError);
			options.onError?.(chatError);
			throw chatError;
		} finally {
			this.abortController = null;
		}
	}

	abortStream(): void {
		if (this.abortController) {
			this.abortController.abort();
			this.abortController = null;
		}
	}

	private getAuthHeaders(): Record<string, string> {
		const headers: Record<string, string> = {};

		if (typeof window !== "undefined") {
			// Try to get auth token from localStorage (common pattern)
			const token = localStorage.getItem("authToken") || localStorage.getItem("token");
			if (token) {
				headers["Authorization"] = `Bearer ${token}`;
			}

			// Get CSRF token if available
			const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");
			if (csrfToken) {
				headers["X-CSRF-Token"] = csrfToken;
			}
		}

		return headers;
	}

	isStreaming(): boolean {
		return this.abortController !== null;
	}
}

export const chatStreamService = new StreamingIAService();
