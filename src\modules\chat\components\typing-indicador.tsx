import { cn } from "@/shared/lib/shadcn/utils";
import { Bo<PERSON> } from "lucide-react";

interface TypingIndicatorProps {
	className?: string;
}

export const TypingIndicator = ({ className }: TypingIndicatorProps) => {
	return (
		<div className={cn("flex gap-3 p-4", className)}>
			<div className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
				<Bot className="text-primary h-4 w-4" />
			</div>

			<div className="bg-muted flex items-center gap-1 rounded-lg px-3 py-2">
				<div className="flex gap-1">
					<div className="bg-muted-foreground/60 h-2 w-2 animate-bounce rounded-full [animation-delay:-0.3s]" />
					<div className="bg-muted-foreground/60 h-2 w-2 animate-bounce rounded-full [animation-delay:-0.15s]" />
					<div className="bg-muted-foreground/60 h-2 w-2 animate-bounce rounded-full" />
				</div>
				<span className="text-muted-foreground ml-2 text-xs">IA está digitando...</span>
			</div>
		</div>
	);
};
