import { cn } from "@/shared/lib/shadcn/utils";
import { motion } from "framer-motion";
import { Bo<PERSON> } from "lucide-react";

interface TypingIndicatorProps {
	className?: string;
}

const dotVariants = {
	initial: { y: 0 },
	animate: { y: -8 },
};

const dotTransition = {
	duration: 0.5,
	repeat: Infinity,
	repeatType: "reverse" as const,
	ease: "easeInOut" as const,
};

export const TypingIndicator = ({ className }: TypingIndicatorProps) => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20, scale: 0.9 }}
			animate={{ opacity: 1, y: 0, scale: 1 }}
			exit={{ opacity: 0, y: -20, scale: 0.9 }}
			transition={{ duration: 0.3, type: "spring" }}
			className={cn("flex gap-3 p-4", className)}
		>
			<motion.div
				initial={{ scale: 0, rotate: -180 }}
				animate={{ scale: 1, rotate: 0 }}
				transition={{ delay: 0.1, duration: 0.3, type: "spring" }}
				className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full"
			>
				<Bot className="text-primary h-4 w-4" />
			</motion.div>

			<motion.div
				initial={{ scale: 0.9, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={{ delay: 0.15, duration: 0.2 }}
				className="bg-muted flex items-center gap-1 rounded-lg px-3 py-2"
			>
				<div className="flex gap-1">
					<motion.div
						variants={dotVariants}
						initial="initial"
						animate="animate"
						transition={{ ...dotTransition, delay: 0 }}
						className="bg-muted-foreground/60 h-2 w-2 rounded-full"
					/>
					<motion.div
						variants={dotVariants}
						initial="initial"
						animate="animate"
						transition={{ ...dotTransition, delay: 0.1 }}
						className="bg-muted-foreground/60 h-2 w-2 rounded-full"
					/>
					<motion.div
						variants={dotVariants}
						initial="initial"
						animate="animate"
						transition={{ ...dotTransition, delay: 0.2 }}
						className="bg-muted-foreground/60 h-2 w-2 rounded-full"
					/>
				</div>
				<motion.span
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.3, duration: 0.3 }}
					className="text-muted-foreground ml-2 text-xs"
				>
					IA está digitando...
				</motion.span>
			</motion.div>
		</motion.div>
	);
};
