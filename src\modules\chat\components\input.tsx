import { Button } from "@/shared/components/shadcn/button";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { cn } from "@/shared/lib/shadcn/utils";
import { motion, AnimatePresence } from "framer-motion";
import { Send, Square } from "lucide-react";
import { forwardRef, KeyboardEvent, useCallback } from "react";

interface ChatInputProps {
	value: string;
	onChange: (value: string) => void;
	onSend: () => void;
	onStop?: () => void;
	disabled?: boolean;
	isStreaming?: boolean;
	placeholder?: string;
	className?: string;
}

export const ChatInput = forwardRef<HTMLTextAreaElement, ChatInputProps>(
	({ value, onChange, onSend, onStop, disabled = false, isStreaming = false, placeholder = "Digite sua mensagem...", className }, ref) => {
		const handleKeyDown = useCallback(
			(e: KeyboardEvent<HTMLTextAreaElement>) => {
				if (e.key === "Enter" && !e.shiftKey) {
					e.preventDefault();
					if (!disabled && !isStreaming && value.trim()) {
						onSend();
					}
				}
			},
			[disabled, isStreaming, value, onSend],
		);

		const handleSend = useCallback(() => {
			if (!disabled && !isStreaming && value.trim()) {
				onSend();
			}
		}, [disabled, isStreaming, value, onSend]);

		const handleStop = useCallback(() => {
			if (isStreaming && onStop) {
				onStop();
			}
		}, [isStreaming, onStop]);

		const canSend = !disabled && !isStreaming && value.trim().length > 0;
		const canStop = isStreaming && onStop;

		return (
			<motion.div
				initial={{ y: 20, opacity: 0 }}
				animate={{ y: 0, opacity: 1 }}
				transition={{ duration: 0.3, type: "spring" }}
				className={cn("bg-background flex gap-2 border-t p-4", className)}
			>
				<motion.div className="flex-1" whileFocus={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
					<Textarea
						ref={ref}
						value={value}
						onChange={e => onChange(e.target.value)}
						onKeyDown={handleKeyDown}
						placeholder={placeholder}
						disabled={disabled}
						className="focus:ring-primary/20 max-h-32 min-h-[44px] resize-none transition-all duration-200 focus:ring-2"
						rows={1}
					/>
				</motion.div>

				<AnimatePresence mode="wait">
					{canStop ? (
						<motion.div
							key="stop"
							initial={{ scale: 0, rotate: 180 }}
							animate={{ scale: 1, rotate: 0 }}
							exit={{ scale: 0, rotate: -180 }}
							transition={{ duration: 0.2, type: "spring" }}
						>
							<Button
								onClick={handleStop}
								size="sm"
								variant="outline"
								className="hover:bg-destructive/10 hover:text-destructive h-11 w-11 shrink-0 p-0"
								aria-label="Parar geração"
							>
								<Square className="h-4 w-4" />
							</Button>
						</motion.div>
					) : (
						<motion.div
							key="send"
							initial={{ scale: 0, rotate: -180 }}
							animate={{ scale: 1, rotate: 0 }}
							exit={{ scale: 0, rotate: 180 }}
							transition={{ duration: 0.2, type: "spring" }}
							whileHover={{ scale: canSend ? 1.05 : 1 }}
							whileTap={{ scale: canSend ? 0.95 : 1 }}
						>
							<Button
								onClick={handleSend}
								disabled={!canSend}
								size="sm"
								className="h-11 w-11 shrink-0 p-0 transition-all duration-200"
								aria-label="Enviar mensagem"
							>
								<motion.div animate={canSend ? { x: [0, 2, 0] } : {}} transition={{ duration: 2, repeat: Infinity }}>
									<Send className="h-4 w-4" />
								</motion.div>
							</Button>
						</motion.div>
					)}
				</AnimatePresence>
			</motion.div>
		);
	},
);

ChatInput.displayName = "ChatInput";
