import { Button } from "@/shared/components/shadcn/button";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { cn } from "@/shared/lib/shadcn/utils";
import { Send, Square } from "lucide-react";
import { forwardRef, KeyboardEvent, useCallback } from "react";

interface ChatInputProps {
	value: string;
	onChange: (value: string) => void;
	onSend: () => void;
	onStop?: () => void;
	disabled?: boolean;
	isStreaming?: boolean;
	placeholder?: string;
	className?: string;
}

export const ChatInput = forwardRef<HTMLTextAreaElement, ChatInputProps>(
	({ value, onChange, onSend, onStop, disabled = false, isStreaming = false, placeholder = "Digite sua mensagem...", className }, ref) => {
		const handleKeyDown = useCallback(
			(e: KeyboardEvent<HTMLTextAreaElement>) => {
				if (e.key === "Enter" && !e.shiftKey) {
					e.preventDefault();
					if (!disabled && !isStreaming && value.trim()) {
						onSend();
					}
				}
			},
			[disabled, isStreaming, value, onSend],
		);

		const handleSend = useCallback(() => {
			if (!disabled && !isStreaming && value.trim()) {
				onSend();
			}
		}, [disabled, isStreaming, value, onSend]);

		const handleStop = useCallback(() => {
			if (isStreaming && onStop) {
				onStop();
			}
		}, [isStreaming, onStop]);

		const canSend = !disabled && !isStreaming && value.trim().length > 0;
		const canStop = isStreaming && onStop;

		return (
			<div className={cn("bg-background flex gap-2 border-t p-4", className)}>
				<Textarea
					ref={ref}
					value={value}
					onChange={e => onChange(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					disabled={disabled}
					className="max-h-32 min-h-[44px] resize-none"
					rows={1}
				/>

				{canStop ? (
					<Button onClick={handleStop} size="sm" variant="outline" className="h-11 w-11 shrink-0 p-0" aria-label="Parar geração">
						<Square className="h-4 w-4" />
					</Button>
				) : (
					<Button onClick={handleSend} disabled={!canSend} size="sm" className="h-11 w-11 shrink-0 p-0" aria-label="Enviar mensagem">
						<Send className="h-4 w-4" />
					</Button>
				)}
			</div>
		);
	},
);

ChatInput.displayName = "ChatInput";
