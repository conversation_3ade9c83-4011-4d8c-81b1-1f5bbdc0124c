import { cn } from "@/shared/lib/shadcn/utils";
import { AnimatePresence, motion } from "framer-motion";
import { Bot, User } from "lucide-react";
import { memo, useEffect, useState } from "react";
import type { IChatMessage } from "../types/chat.types";

interface ChatMessageProps {
	message: IChatMessage;
	isStreaming?: boolean;
}

// Componente para animação de texto digitando
const TypewriterText = memo<{ text: string; isStreaming: boolean }>(({ text, isStreaming }) => {
	const [displayedText, setDisplayedText] = useState("");
	const [currentIndex, setCurrentIndex] = useState(0);

	useEffect(() => {
		if (isStreaming && currentIndex < text.length) {
			// Velocidade variável baseada no tipo de caractere
			const char = text[currentIndex];
			let delay = 30; // Velocidade base

			if (char === " ")
				delay = 15; // Espaços mais rápidos
			else if (char === "\n")
				delay = 50; // Quebras de linha mais lentas
			else if (/[.!?]/.test(char))
				delay = 200; // Pontuação final mais lenta
			else if (/[,;:]/.test(char))
				delay = 100; // Pontuação média
			else if (/[a-zA-Z0-9]/.test(char)) delay = 25; // Letras e números normais

			const timer = setTimeout(() => {
				setDisplayedText(text.slice(0, currentIndex + 1));
				setCurrentIndex(prev => prev + 1);
			}, delay);

			return () => clearTimeout(timer);
		} else if (!isStreaming) {
			// Se não está mais streaming, mostra o texto completo imediatamente
			setDisplayedText(text);
			setCurrentIndex(text.length);
		}
	}, [text, currentIndex, isStreaming]);

	// Reset quando o texto muda (nova mensagem)
	useEffect(() => {
		if (isStreaming) {
			setDisplayedText("");
			setCurrentIndex(0);
		}
	}, [text, isStreaming]);

	return (
		<span>
			{displayedText}
			{isStreaming && (
				<motion.span
					animate={{ opacity: [1, 0] }}
					transition={{
						duration: 0.8,
						repeat: Infinity,
						repeatType: "reverse",
						ease: "easeInOut",
					}}
					className="ml-1 inline-block h-4 w-0.5 bg-current"
				/>
			)}
		</span>
	);
});

TypewriterText.displayName = "TypewriterText";

export const ChatMessage = memo<ChatMessageProps>(({ message, isStreaming = false }) => {
	const isUser = message.role === "user";
	const isAssistant = message.role === "assistant";

	return (
		<motion.div
			initial={{ opacity: 0, y: 20, scale: 0.95 }}
			animate={{ opacity: 1, y: 0, scale: 1 }}
			transition={{
				duration: 0.3,
				ease: "easeOut",
				type: "spring",
				stiffness: 500,
				damping: 30,
			}}
			className={cn("flex gap-3 p-4", isUser ? "justify-end" : "justify-start")}
		>
			<AnimatePresence>
				{isAssistant && (
					<motion.div
						initial={{ scale: 0, rotate: -180 }}
						animate={{ scale: 1, rotate: 0 }}
						transition={{ delay: 0.1, duration: 0.3, type: "spring" }}
						className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full"
					>
						<Bot className="text-primary h-4 w-4" />
					</motion.div>
				)}
			</AnimatePresence>

			<motion.div
				initial={{ scale: 0.9, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={{ delay: 0.15, duration: 0.2 }}
				className={cn(
					"max-w-[80%] rounded-lg px-3 py-2 text-sm transition-all duration-200",
					isUser ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
					message.isError && "bg-destructive/10 text-destructive border-destructive/20 border",
				)}
			>
				<div className="break-words whitespace-pre-wrap">
					{isAssistant && isStreaming ? (
						<TypewriterText text={message.content} isStreaming={isStreaming} />
					) : (
						<motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.2 }}>
							{message.content}
						</motion.span>
					)}
				</div>
				{!isStreaming && message.timestamp && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 0.7 }}
						transition={{ delay: 0.5, duration: 0.3 }}
						className="mt-1 text-xs opacity-70"
					>
						{message.timestamp.toLocaleTimeString([], {
							hour: "2-digit",
							minute: "2-digit",
						})}
					</motion.div>
				)}
			</motion.div>

			<AnimatePresence>
				{isUser && (
					<motion.div
						initial={{ scale: 0, rotate: 180 }}
						animate={{ scale: 1, rotate: 0 }}
						transition={{ delay: 0.1, duration: 0.3, type: "spring" }}
						className="bg-primary/10 flex h-8 w-8 shrink-0 items-center justify-center rounded-full"
					>
						<User className="text-primary h-4 w-4" />
					</motion.div>
				)}
			</AnimatePresence>
		</motion.div>
	);
});

ChatMessage.displayName = "ChatMessage";
